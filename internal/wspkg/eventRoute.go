package wspkg

import "fmt"

func (W *WebSocketCtx) WebSocketEventRoute() {
	// Register the WebSocket event route
	W.manger.router.On("isOpen", W.OnIsOpen)
	W.manger.router.On("notify", W.OnNotify)
}

func (W *WebSocketCtx) OnIsOpen(c Ctx) error {
	doorClient, err := W.ClientState.GetDoorClient(c.GetClientID())
	if err != nil {
		W.manger.logger.Error(
			"Door client not found for isOpen event",
			Field{Key: "client_id", Value: c.GetClientID()},
			Field{Key: "error", Value: err},
		)
		return nil // Don't return error to prevent disconnection
	}

	data := map[string]interface{}{"isOpen": doorClient.IsOpen()}
	msg, err := NewMessageFromJSON("isOpen", data)
	if err != nil {
		W.manger.logger.Error(
			"Error creating message for isOpen event",
			Field{Key: "client_id", Value: c.GetClientID()},
			Field{Key: "error", Value: err},
		)
		return nil // Don't return error to prevent disconnection
	}

	err = c.WriteMessage(msg)
	if err != nil {
		return err // Return error to handle it in the caller
	}
	W.manger.logger.Debug(
		"isOpen event handled successfully",
		Field{Key: "client_id", Value: c.GetClientID()},
		Field{Key: "isOpen", Value: doorClient.IsOpen()},
	)

	msg1, err := NewMessageFromJSON("notify", data)
	if err != nil {
		W.manger.logger.Error(
			"Error creating message for isOpen event",
			Field{Key: "client_id", Value: c.GetClientID()},
			Field{Key: "error", Value: err},
		)
		return nil // Don't return error to prevent disconnection
	}
	err = W.Pubsub.Publish(fmt.Sprintf("device-%d", doorClient.DoorId), msg1)
	if err != nil {
		return err // Return error to handle it in the caller
	}
	W.manger.logger.Debug(
		"Event published to PubSub",
		Field{Key: "client_id", Value: c.GetClientID()},
		Field{Key: "isOpen", Value: doorClient.IsOpen()},
	)
	return nil
}

func (W *WebSocketCtx) OnNotify(c Ctx) error {
	W.manger.logger.Debug("log", Field{Key: "client", Value: c.client.Id})
	err := c.WriteMessage(c.Data)
	if err != nil {
		return err // Return error to handle it in the caller
	}
	W.manger.logger.Debug(
		"isOpen event handled successfully",
		Field{Key: "client_id", Value: c.GetClientID()},
	)
	return nil
}
